# Configuration Nginx pour MD Agricole
# Fichier à placer dans /etc/nginx/sites-available/mdagricole.zidani.org
# Puis créer un lien symbolique : ln -s /etc/nginx/sites-available/mdagricole.zidani.org /etc/nginx/sites-enabled/

# Redirection HTTP vers HTTPS
server {
    listen 80;
    listen [::]:80;
    server_name mdagricole.zidani.org www.mdagricole.zidani.org;
    
    # Redirection permanente vers HTTPS
    return 301 https://$server_name$request_uri;
}

# Configuration HTTPS principale
server {
    listen 443 ssl http2;
    listen [::]:443 ssl http2;
    server_name mdagricole.zidani.org www.mdagricole.zidani.org;

    # Certificats SSL (Let's Encrypt recommandé)
   # ssl_certificate /etc/letsencrypt/live/mdagricole.zidani.org/fullchain.pem;
    #ssl_certificate_key /etc/letsencrypt/live/mdagricole.zidani.org/privkey.pem;
    
    # Configuration SSL moderne
    ssl_protocols TLSv1.2 TLSv1.3;
    ssl_ciphers ECDHE-RSA-AES128-GCM-SHA256:ECDHE-RSA-AES256-GCM-SHA384:ECDHE-RSA-AES128-SHA256:ECDHE-RSA-AES256-SHA384;
    ssl_prefer_server_ciphers off;
    ssl_session_cache shared:SSL:10m;
    ssl_session_timeout 10m;
    
    # Sécurité headers
    add_header X-Frame-Options "SAMEORIGIN" always;
    add_header X-Content-Type-Options "nosniff" always;
    add_header X-XSS-Protection "1; mode=block" always;
    add_header Referrer-Policy "strict-origin-when-cross-origin" always;
    add_header Content-Security-Policy "default-src 'self'; script-src 'self' 'unsafe-inline' 'unsafe-eval'; style-src 'self' 'unsafe-inline'; img-src 'self' data: https:; font-src 'self' data:; connect-src 'self'; frame-ancestors 'self';" always;
    
    # Gzip compression
    gzip on;
    gzip_vary on;
    gzip_min_length 1024;
    gzip_proxied any;
    gzip_comp_level 6;
    gzip_types
        text/plain
        text/css
        text/xml
        text/javascript
        application/json
        application/javascript
        application/xml+rss
        application/atom+xml
        image/svg+xml;

    # Taille maximale des uploads
    client_max_body_size 50M;

    # Logs
    access_log /var/log/nginx/mdagricole.access.log;
    error_log /var/log/nginx/mdagricole.error.log;

    # Proxy vers l'application Next.js
    location / {
        proxy_pass http://localhost:3007;
        proxy_http_version 1.1;
        proxy_set_header Upgrade $http_upgrade;
        proxy_set_header Connection 'upgrade';
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
        proxy_cache_bypass $http_upgrade;
        
        # Timeouts
        proxy_connect_timeout 60s;
        proxy_send_timeout 60s;
        proxy_read_timeout 60s;
    }

    # Servir les fichiers statiques directement
    location /_next/static/ {
        proxy_pass http://localhost:3007;
        proxy_cache_valid 200 1y;
        add_header Cache-Control "public, immutable";
    }

    # Servir les uploads directement
    location /uploads/ {
        proxy_pass http://localhost:3007;
        proxy_cache_valid 200 1d;
        add_header Cache-Control "public, max-age=86400";
    }

    # API routes
    location /api/ {
        proxy_pass http://localhost:3007;
        proxy_set_header Host $host;
        proxy_set_header X-Real-IP $remote_addr;
        proxy_set_header X-Forwarded-For $proxy_add_x_forwarded_for;
        proxy_set_header X-Forwarded-Proto $scheme;
    }

    # Favicon et robots.txt
    location = /favicon.ico {
        proxy_pass http://localhost:3007;
        access_log off;
    }

    location = /robots.txt {
        proxy_pass http://localhost:3007;
        access_log off;
    }

    # Sécurité - Bloquer l'accès aux fichiers sensibles
    location ~ /\. {
        deny all;
        access_log off;
        log_not_found off;
    }

    location ~ \.(env|log|sql)$ {
        deny all;
        access_log off;
        log_not_found off;
    }
}
