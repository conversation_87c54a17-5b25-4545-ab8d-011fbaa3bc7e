
import Link from 'next/link';
import { Package, Phone, Mail, MapPin, Facebook, Instagram, Linkedin, Tractor } from 'lucide-react';

export function Footer() {
  return (
    <footer className="bg-gradient-to-br from-primary/5 via-agricultural-50/50 to-earth-50/50 border-t border-border/50">
      {/* Top Wave */}
      <div className="relative">
        <svg viewBox="0 0 1200 120" preserveAspectRatio="none" className="w-full h-16 fill-white">
          <path d="M0,0V46.29c47.79,22.2,103.59,32.17,158,28,70.36-5.37,136.33-33.31,206.8-37.5C438.64,32.43,512.34,53.67,583,72.05c69.27,18,138.3,24.88,209.4,13.08,36.15-6,69.85-17.84,104.45-29.34C989.49,25,1113-14.29,1200,52.47V0Z" opacity=".25"></path>
          <path d="M0,0V15.81C13,36.92,27.64,56.86,47.69,72.05,99.41,111.27,165,111,224.58,91.58c31.15-10.15,60.09-26.07,89.67-39.8,40.92-19,84.73-46,130.83-49.67,36.26-2.85,70.9,9.42,98.6,31.56,31.77,25.39,62.32,62,103.63,73,40.44,10.79,81.35-6.69,119.13-24.28s75.16-39,116.92-43.05c59.73-5.85,113.28,22.88,168.9,38.84,30.2,8.66,59,6.17,87.09-7.5,22.43-10.89,48-26.93,60.65-49.24V0Z" opacity=".5"></path>
          <path d="M0,0V5.63C149.93,59,314.09,71.32,475.83,42.57c43-7.64,84.23-20.12,127.61-26.46,59-8.63,112.48,12.24,165.56,35.4C827.93,77.22,886,95.24,951.2,90c86.53-7,172.46-45.71,248.8-84.81V0Z"></path>
        </svg>
      </div>

      <div className="container-custom py-16">
        <div className="grid grid-cols-1 md:grid-cols-2 lg:grid-cols-4 gap-12">
          {/* Logo et description */}
          <div className="lg:col-span-2 space-y-6">
            <div className="flex items-center space-x-3">
              <div className="w-12 h-12 bg-gradient-to-br from-primary to-agricultural-600 rounded-xl flex items-center justify-center shadow-medium">
                <Package className="h-7 w-7 text-white" />
              </div>
              <div className="flex flex-col">
                <span className="text-2xl font-bold text-gradient">MD Agricole</span>
                <span className="text-sm text-muted-foreground font-medium">Matériel Agricole</span>
              </div>
            </div>
            <p className="text-muted-foreground text-lg leading-relaxed max-w-md">
              Votre partenaire de confiance pour tout matériel agricole en Tunisie.
              Qualité, fiabilité et service d'excellence depuis plus de 15 ans.
            </p>

            {/* Social Media */}
            <div className="flex space-x-4">
              <a href="#" className="w-10 h-10 bg-primary/10 hover:bg-primary hover:text-white rounded-lg flex items-center justify-center transition-all duration-300 hover-lift">
                <Facebook className="h-5 w-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-primary/10 hover:bg-primary hover:text-white rounded-lg flex items-center justify-center transition-all duration-300 hover-lift">
                <Instagram className="h-5 w-5" />
              </a>
              <a href="#" className="w-10 h-10 bg-primary/10 hover:bg-primary hover:text-white rounded-lg flex items-center justify-center transition-all duration-300 hover-lift">
                <Linkedin className="h-5 w-5" />
              </a>
            </div>
          </div>

          {/* Navigation */}
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-foreground">Navigation</h3>
            <div className="flex flex-col space-y-3">
              <Link href="/" className="text-muted-foreground hover:text-primary transition-all duration-200 hover:translate-x-1 flex items-center space-x-2">
                <span>Accueil</span>
              </Link>
              <Link href="/catalogue" className="text-muted-foreground hover:text-primary transition-all duration-200 hover:translate-x-1 flex items-center space-x-2">
                <span>Catalogue</span>
              </Link>
              <Link href="/contact" className="text-muted-foreground hover:text-primary transition-all duration-200 hover:translate-x-1 flex items-center space-x-2">
                <span>Contact</span>
              </Link>
              <Link href="/auth/connexion" className="text-muted-foreground hover:text-primary transition-all duration-200 hover:translate-x-1 flex items-center space-x-2">
                <span>Connexion</span>
              </Link>
            </div>
          </div>

          {/* Contact */}
          <div className="space-y-6">
            <h3 className="text-xl font-bold text-foreground">Contact</h3>
            <div className="flex flex-col space-y-4">
              <div className="flex items-start space-x-3 text-muted-foreground group">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <Phone className="h-4 w-4" />
                </div>
                <div>
                  <p className="font-medium">+216 71 123 456</p>
                  <p className="text-sm">Lun-Ven: 8h-18h</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 text-muted-foreground group">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <Mail className="h-4 w-4" />
                </div>
                <div>
                  <p className="font-medium"><EMAIL></p>
                  <p className="text-sm">Réponse sous 24h</p>
                </div>
              </div>
              <div className="flex items-start space-x-3 text-muted-foreground group">
                <div className="w-8 h-8 bg-primary/10 rounded-lg flex items-center justify-center group-hover:bg-primary/20 transition-colors">
                  <MapPin className="h-4 w-4" />
                </div>
                <div>
                  <p className="font-medium">Tunis, Tunisie</p>
                  <p className="text-sm">Zone industrielle</p>
                </div>
              </div>
            </div>
          </div>
        </div>

        {/* Bottom Section */}
        <div className="border-t border-border/50 mt-16 pt-8">
          <div className="flex flex-col md:flex-row justify-between items-center space-y-4 md:space-y-0">
            <div className="flex items-center space-x-4 text-muted-foreground">
              <Tractor className="h-5 w-5 text-primary" />
              <p>&copy; 2024 MD Agricole. Tous droits réservés.</p>
            </div>
            <div className="flex items-center space-x-6 text-sm text-muted-foreground">
              <Link href="#" className="hover:text-primary transition-colors">
                Politique de confidentialité
              </Link>
              <Link href="#" className="hover:text-primary transition-colors">
                Conditions d'utilisation
              </Link>
            </div>
          </div>
        </div>
      </div>
    </footer>
  );
}
