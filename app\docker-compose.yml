version: '3.8'

services:
  # Service de base de données PostgreSQL
  db:
    image: postgres:15-alpine
    container_name: md_agricole_db
    restart: unless-stopped
    environment:
      POSTGRES_USER: md_user
      POSTGRES_PASSWORD: md_password_2024
      POSTGRES_DB: md_agricole_db
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data:/var/lib/postgresql/data
    ports:
      - "5437:5432"
    networks:
      - md_network
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U md_user -d md_agricole_db"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Service de l'application Next.js
  app:
    build:
      context: .
      dockerfile: Dockerfile
    container_name: md_agricole_app
    restart: unless-stopped
    ports:
      - "3007:3000"
    environment:
      - NODE_ENV=production
      - DATABASE_URL=*********************************************/md_agricole_db?schema=public
      - NEXTAUTH_URL=https://mdagricole.zidani.org
      - NEXTAUTH_SECRET=your-super-secret-key-change-this-in-production
      - NEXTAUTH_TRUST_HOST=true
    depends_on:
      db:
        condition: service_healthy
    networks:
      - md_network
    # Volumes pour le développement (commenté pour la production)
    # volumes:
    #   - .:/app
    #   - /app/node_modules
    #   - /app/.next

# Réseaux
networks:
  md_network:
    driver: bridge

# Volumes persistants
volumes:
  postgres_data:
    driver: local
