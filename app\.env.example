# 🚜 Configuration MD Agricole - Fichier d'exemple
# Copiez ce fichier vers .env et modifiez les valeurs selon votre environnement

# =============================================================================
# BASE DE DONNÉES POSTGRESQL
# =============================================================================

# URL de connexion à PostgreSQL
# Format: postgresql://[utilisateur]:[mot_de_passe]@[host]:[port]/[nom_base]?schema=public
DATABASE_URL="postgresql://md_user:md_password_2024@localhost:5432/md_agricole_db?schema=public"

# Pour Docker (par défaut)
# DATABASE_URL="postgresql://md_user:md_password_2024@localhost:5432/md_agricole_db?schema=public"

# Pour une installation locale de PostgreSQL
# DATABASE_URL="postgresql://postgres:votre_mot_de_passe@localhost:5432/md_agricole_db?schema=public"

# Pour un serveur distant
# DATABASE_URL="postgresql://utilisateur:<EMAIL>:5432/md_agricole_db?schema=public"

# =============================================================================
# NEXTAUTH.JS - AUTHENTIFICATION
# =============================================================================

# URL de base de l'application
NEXTAUTH_URL="http://localhost:3007"

# Clé secrète pour NextAuth.js (OBLIGATOIRE)
# Générez une clé sécurisée avec: openssl rand -base64 32
# Ou utilisez: https://generate-secret.vercel.app/32
NEXTAUTH_SECRET="changez-cette-cle-secrete-en-production-elle-doit-etre-longue-et-aleatoire"

# =============================================================================
# CONFIGURATION OPTIONNELLE
# =============================================================================

# Environnement (development, production, test)
NODE_ENV="development"

# Port de l'application (optionnel, par défaut 3000)
# PORT=3000

# =============================================================================
# INTÉGRATIONS TIERCES (Optionnel)
# =============================================================================

# Configuration email (pour les notifications)
# SMTP_HOST="smtp.gmail.com"
# SMTP_PORT="587"
# SMTP_USER="<EMAIL>"
# SMTP_PASSWORD="votre-mot-de-passe-app"

# Configuration de stockage de fichiers (optionnel)
# UPLOAD_DIR="./public/uploads"
# MAX_FILE_SIZE="5242880" # 5MB en bytes

# =============================================================================
# NOTES IMPORTANTES
# =============================================================================

# 1. Ne jamais commiter le fichier .env dans Git
# 2. Utiliser des mots de passe forts en production
# 3. Changer NEXTAUTH_SECRET en production
# 4. Sauvegarder ces configurations de manière sécurisée

# =============================================================================
# EXEMPLES DE CONFIGURATION PAR ENVIRONNEMENT
# =============================================================================

# DÉVELOPPEMENT LOCAL:
# DATABASE_URL="postgresql://md_user:md_password_2024@localhost:5432/md_agricole_db?schema=public"
# NEXTAUTH_URL="http://localhost:3000"

# PRODUCTION:
# DATABASE_URL="*******************************************/md_agricole_prod?schema=public"
# NEXTAUTH_URL="https://votre-domaine.com"

# TEST:
# DATABASE_URL="postgresql://test_user:test_password@localhost:5432/md_agricole_test?schema=public"
# NEXTAUTH_URL="http://localhost:3001"
