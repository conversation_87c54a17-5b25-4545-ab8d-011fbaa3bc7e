generator client {
  provider      = "prisma-client-js"
  binaryTargets = ["native", "linux-musl-arm64-openssl-3.0.x"]
}

datasource db {
  provider = "postgresql"
  url      = env("DATABASE_URL")
}

model User {
  id           String   @id @default(cuid())
  email        String   @unique
  password     String?
  name         String?
  firstName    String?
  lastName     String?
  phone        String?
  address      String?
  city         String?
  postalCode   String?
  image        String?
  idCardNumber String?  @unique
  role         String   @default("CUSTOMER")
  isActive     Boolean  @default(true)
  createdAt    DateTime @default(now())
  updatedAt    DateTime @updatedAt

  accounts Account[]
  sessions Session[]
  orders   Order[]
  invoices Invoice[]

  @@map(name: "users")
}

model Account {
  id                String  @id @default(cuid())
  userId            String
  type              String
  provider          String
  providerAccountId String
  refresh_token     String? @db.Text
  access_token      String? @db.Text
  expires_at        Int?
  token_type        String?
  scope             String?
  id_token          String? @db.Text
  session_state     String?

  user User @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@unique([provider, providerAccountId])
  @@map(name: "accounts")
}

model Session {
  id           String   @id @default(cuid())
  sessionToken String   @unique
  userId       String
  expires      DateTime
  user         User     @relation(fields: [userId], references: [id], onDelete: Cascade)

  @@map(name: "sessions")
}

model VerificationToken {
  identifier String
  token      String   @unique
  expires    DateTime

  @@unique([identifier, token])
  @@map(name: "verification_tokens")
}

model Category {
  id          String   @id @default(cuid())
  name        String
  slug        String   @unique
  description String?
  image       String?
  isActive    Boolean  @default(true)
  createdAt   DateTime @default(now())
  updatedAt   DateTime @updatedAt

  products Product[]

  @@map(name: "categories")
}

model Product {
  id             String   @id @default(cuid())
  name           String
  slug           String   @unique
  description    String?
  images         String[] @default([])
  price          Decimal  @db.Decimal(10, 2)
  brand          String?
  model          String?
  power          String? // Pour les tracteurs (cv)
  weight         String? // Poids en kg
  dimensions     String? // Dimensions (L x l x h)
  features       String[] @default([])
  specifications Json?
  stockQuantity  Int      @default(0)
  isActive       Boolean  @default(true)
  isFeatured     Boolean  @default(false)
  categoryId     String
  createdAt      DateTime @default(now())
  updatedAt      DateTime @updatedAt

  category   Category    @relation(fields: [categoryId], references: [id], onDelete: Cascade)
  orderItems OrderItem[]

  @@map(name: "products")
}

model Order {
  id          String      @id @default(cuid())
  orderNumber String      @unique
  status      OrderStatus @default(PENDING)
  totalAmount Decimal     @db.Decimal(10, 2)
  notes       String?

  // Informations client
  customerName  String
  customerEmail String
  customerPhone String

  // Adresse de livraison
  deliveryAddress    String
  deliveryCity       String
  deliveryPostalCode String?

  userId    String
  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  user       User        @relation(fields: [userId], references: [id], onDelete: Cascade)
  orderItems OrderItem[]
  invoice    Invoice?

  @@map(name: "orders")
}

model OrderItem {
  id        String  @id @default(cuid())
  quantity  Int
  unitPrice Decimal @db.Decimal(10, 2)

  orderId   String
  productId String

  order   Order   @relation(fields: [orderId], references: [id], onDelete: Cascade)
  product Product @relation(fields: [productId], references: [id], onDelete: Cascade)

  @@map(name: "order_items")
}

model Invoice {
  id             String        @id @default(cuid())
  invoiceNumber  String        @unique
  status         InvoiceStatus @default(PENDING)
  totalAmount    Decimal       @db.Decimal(10, 2)
  taxAmount      Decimal       @default(0) @db.Decimal(10, 2)
  discountAmount Decimal       @default(0) @db.Decimal(10, 2)
  notes          String?
  dueDate        DateTime?

  // Relations
  orderId String @unique
  userId  String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  order    Order     @relation(fields: [orderId], references: [id], onDelete: Cascade)
  user     User      @relation(fields: [userId], references: [id], onDelete: Cascade)
  payments Payment[]

  @@map(name: "invoices")
}

model Payment {
  id            String        @id @default(cuid())
  paymentNumber String        @unique
  amount        Decimal       @db.Decimal(10, 2)
  paymentMethod PaymentMethod
  status        PaymentStatus @default(PENDING)
  notes         String?

  // Informations spécifiques selon le type de paiement
  checkNumber   String? // Pour les chèques
  cardLast4     String? // Pour les cartes
  transactionId String? // Pour les paiements électroniques

  // Relations
  invoiceId String

  createdAt DateTime @default(now())
  updatedAt DateTime @updatedAt

  invoice Invoice @relation(fields: [invoiceId], references: [id], onDelete: Cascade)

  @@map(name: "payments")
}

enum OrderStatus {
  PENDING // En attente
  CONFIRMED // Confirmée
  PROCESSING // En cours de traitement
  SHIPPED // Expédiée
  DELIVERED // Livrée
  CANCELLED // Annulée
}

enum InvoiceStatus {
  PENDING // En attente
  SENT // Envoyée
  PAID // Payée
  OVERDUE // En retard
  CANCELLED // Annulée
}

enum PaymentMethod {
  CASH // Espèces
  CHECK // Chèque
  CARD // Carte bancaire
  CREDIT // À crédit
  BANK_TRANSFER // Virement bancaire
}

enum PaymentStatus {
  PENDING // En attente
  COMPLETED // Terminé
  FAILED // Échoué
  CANCELLED // Annulé
}
