version: '3.8'

services:
  # Service de base de données PostgreSQL pour le développement
  db:
    image: postgres:16-alpine
    container_name: md_agricole_db_dev
    restart: unless-stopped
    environment:
      POSTGRES_USER: md_user
      POSTGRES_PASSWORD: md_password_2024
      POSTGRES_DB: md_agricole_db_dev
      POSTGRES_INITDB_ARGS: "--encoding=UTF-8 --lc-collate=C --lc-ctype=C"
    volumes:
      - postgres_data_dev:/var/lib/postgresql/data
    ports:
      - "5433:5432"
    networks:
      - md_network_dev
    healthcheck:
      test: ["CMD-SHELL", "pg_isready -U md_user -d md_agricole_db_dev"]
      interval: 10s
      timeout: 5s
      retries: 5

  # Service de l'application Next.js pour le développement
  app:
    build:
      context: .
      dockerfile: Dockerfile.dev
    container_name: md_agricole_app_dev
    restart: unless-stopped
    ports:
      - "3001:3000"
    environment:
      - NODE_ENV=development
      - DATABASE_URL=*********************************************/md_agricole_db_dev?schema=public
      - NEXTAUTH_URL=http://localhost:3001
      - NEXTAUTH_SECRET=dev-secret-key-not-for-production
    depends_on:
      db:
        condition: service_healthy
    networks:
      - md_network_dev
    volumes:
      - .:/app
      - /app/node_modules
      - /app/.next

# Réseaux pour le développement
networks:
  md_network_dev:
    driver: bridge

# Volumes persistants pour le développement
volumes:
  postgres_data_dev:
    driver: local
