-- Script d'initialisation de la base de données MD Agricole
-- Ce script sera exécuté automatiquement lors du premier démarrage de PostgreSQL

-- Créer la base de données si elle n'existe pas
SELECT 'CREATE DATABASE md_agricole'
WHERE NOT EXISTS (SELECT FROM pg_database WHERE datname = 'md_agricole')\gexec

-- Se connecter à la base de données
\c md_agricole;

-- Créer les extensions nécessaires
CREATE EXTENSION IF NOT EXISTS "uuid-ossp";
CREATE EXTENSION IF NOT EXISTS "pg_trgm";

-- Créer un utilisateur de lecture seule pour les rapports (optionnel)
DO $$
BEGIN
    IF NOT EXISTS (SELECT FROM pg_catalog.pg_roles WHERE rolname = 'md_readonly') THEN
        CREATE ROLE md_readonly WITH LOGIN PASSWORD 'readonly_password_2024';
    END IF;
END
$$;

-- Accorder les permissions de lecture
GRANT CONNECT ON DATABASE md_agricole TO md_readonly;
GRANT USAGE ON SCHEMA public TO md_readonly;
GRANT SELECT ON ALL TABLES IN SCHEMA public TO md_readonly;
ALTER DEFAULT PRIVILEGES IN SCHEMA public GRANT SELECT ON TABLES TO md_readonly;

-- Créer des index pour améliorer les performances
-- Ces index seront créés après que Prisma ait créé les tables

-- Fonction pour créer les index après la création des tables
CREATE OR REPLACE FUNCTION create_performance_indexes()
RETURNS void AS $$
BEGIN
    -- Index pour les recherches de produits
    CREATE INDEX IF NOT EXISTS idx_products_name_search ON products USING gin(name gin_trgm_ops);
    CREATE INDEX IF NOT EXISTS idx_products_category_active ON products(category_id, is_active);
    CREATE INDEX IF NOT EXISTS idx_products_price ON products(price);
    
    -- Index pour les utilisateurs
    CREATE INDEX IF NOT EXISTS idx_users_email_search ON users USING gin(email gin_trgm_ops);
    CREATE INDEX IF NOT EXISTS idx_users_name_search ON users USING gin((first_name || ' ' || last_name) gin_trgm_ops);
    CREATE INDEX IF NOT EXISTS idx_users_phone ON users(phone);
    CREATE INDEX IF NOT EXISTS idx_users_active ON users(is_active);
    
    -- Index pour les commandes
    CREATE INDEX IF NOT EXISTS idx_orders_user_date ON orders(user_id, created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_orders_status_date ON orders(status, created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_orders_number_search ON orders USING gin(order_number gin_trgm_ops);
    
    -- Index pour les factures
    CREATE INDEX IF NOT EXISTS idx_invoices_user_date ON invoices(user_id, created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_invoices_status_date ON invoices(status, created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_invoices_due_date ON invoices(due_date) WHERE status IN ('PENDING', 'SENT');
    
    -- Index pour les paiements
    CREATE INDEX IF NOT EXISTS idx_payments_invoice_date ON payments(invoice_id, created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_payments_method_date ON payments(payment_method, created_at DESC);
    CREATE INDEX IF NOT EXISTS idx_payments_status ON payments(status);
    
    RAISE NOTICE 'Index de performance créés avec succès';
END;
$$ LANGUAGE plpgsql;

-- Fonction pour créer les vues utiles
CREATE OR REPLACE FUNCTION create_useful_views()
RETURNS void AS $$
BEGIN
    -- Vue pour les statistiques des utilisateurs
    CREATE OR REPLACE VIEW user_stats AS
    SELECT 
        u.id,
        u.email,
        u.first_name,
        u.last_name,
        u.phone,
        u.city,
        u.is_active,
        u.created_at,
        COUNT(DISTINCT o.id) as total_orders,
        COUNT(DISTINCT i.id) as total_invoices,
        COALESCE(SUM(o.total_amount), 0) as total_spent,
        COUNT(DISTINCT CASE WHEN o.status = 'DELIVERED' THEN o.id END) as completed_orders,
        MAX(o.created_at) as last_order_date
    FROM users u
    LEFT JOIN orders o ON u.id = o.user_id
    LEFT JOIN invoices i ON u.id = i.user_id
    WHERE u.role = 'CUSTOMER'
    GROUP BY u.id, u.email, u.first_name, u.last_name, u.phone, u.city, u.is_active, u.created_at;

    -- Vue pour les statistiques des produits
    CREATE OR REPLACE VIEW product_stats AS
    SELECT 
        p.id,
        p.name,
        p.price,
        p.stock_quantity,
        p.is_active,
        c.name as category_name,
        COUNT(DISTINCT oi.id) as total_sold,
        COALESCE(SUM(oi.quantity), 0) as quantity_sold,
        COALESCE(SUM(oi.quantity * oi.unit_price), 0) as revenue_generated,
        MAX(o.created_at) as last_sold_date
    FROM products p
    LEFT JOIN categories c ON p.category_id = c.id
    LEFT JOIN order_items oi ON p.id = oi.product_id
    LEFT JOIN orders o ON oi.order_id = o.id
    GROUP BY p.id, p.name, p.price, p.stock_quantity, p.is_active, c.name;

    -- Vue pour le tableau de bord
    CREATE OR REPLACE VIEW dashboard_stats AS
    SELECT 
        (SELECT COUNT(*) FROM users WHERE role = 'CUSTOMER' AND is_active = true) as active_customers,
        (SELECT COUNT(*) FROM products WHERE is_active = true) as active_products,
        (SELECT COUNT(*) FROM orders WHERE status != 'CANCELLED') as total_orders,
        (SELECT COUNT(*) FROM invoices WHERE status = 'PAID') as paid_invoices,
        (SELECT COALESCE(SUM(total_amount), 0) FROM orders WHERE status = 'DELIVERED') as total_revenue,
        (SELECT COALESCE(SUM(total_amount), 0) FROM invoices WHERE status != 'PAID') as pending_payments,
        (SELECT COUNT(*) FROM orders WHERE created_at >= CURRENT_DATE - INTERVAL '30 days') as orders_last_30_days,
        (SELECT COUNT(*) FROM users WHERE created_at >= CURRENT_DATE - INTERVAL '30 days' AND role = 'CUSTOMER') as new_customers_last_30_days;

    RAISE NOTICE 'Vues utiles créées avec succès';
END;
$$ LANGUAGE plpgsql;

-- Message de bienvenue
DO $$
BEGIN
    RAISE NOTICE '🎉 Base de données MD Agricole initialisée avec succès !';
    RAISE NOTICE '📊 Utilisez les fonctions create_performance_indexes() et create_useful_views() après la création des tables par Prisma';
END;
$$;
