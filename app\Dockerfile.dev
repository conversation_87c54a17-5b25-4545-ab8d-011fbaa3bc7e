# Dockerfile pour le développement
FROM node:20-alpine AS development

# Définir le répertoire de travail
WORKDIR /app

# Installer les dépendances système nécessaires
RUN apk add --no-cache libc6-compat

# Copier les fichiers de configuration des dépendances
COPY package.json yarn.lock* ./
COPY prisma ./prisma/

# Installer les dépendances
RUN yarn install

# Générer le client Prisma
RUN npx prisma generate

# Variables d'environnement pour le développement
ENV NEXT_TELEMETRY_DISABLED=1
ENV NODE_ENV=development

# Exposer le port 3000
EXPOSE 3000

# Commande par défaut pour le développement
CMD ["yarn", "dev"]
