#!/bin/bash

# Script de migration PostgreSQL 15 → 16 pour MD Agricole
# Usage: ./upgrade-postgres.sh

set -e

# Couleurs
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🔄 Migration PostgreSQL 15 → 16 - MD Agricole"
echo "=============================================="

# Vérifications préliminaires
log_info "Vérifications préliminaires..."

if ! docker --version > /dev/null 2>&1; then
    log_error "Docker n'est pas installé ou accessible"
    exit 1
fi

if ! docker-compose --version > /dev/null 2>&1; then
    log_error "Docker Compose n'est pas installé ou accessible"
    exit 1
fi

# Confirmation
echo ""
log_warning "⚠️  ATTENTION : Cette opération va :"
echo "1. Sauvegarder vos données PostgreSQL 15"
echo "2. Supprimer l'ancien volume PostgreSQL"
echo "3. Créer un nouveau volume PostgreSQL 16"
echo "4. Restaurer vos données"
echo ""
read -p "Êtes-vous sûr de vouloir continuer ? (oui/non): " -r
if [[ ! $REPLY =~ ^[Oo][Uu][Ii]$ ]]; then
    echo "Migration annulée."
    exit 0
fi

# 1. Sauvegarde des données PostgreSQL 15
DATE=$(date +%Y%m%d_%H%M%S)
BACKUP_FILE="postgres15_backup_${DATE}.sql"

log_info "1/6 - Sauvegarde des données PostgreSQL 15..."

# Démarrer temporairement avec PostgreSQL 15 pour la sauvegarde
docker-compose down || true

# Modifier temporairement pour PostgreSQL 15 si ce n'est pas déjà fait
if grep -q "postgres:16" docker-compose.yml; then
    log_info "Modification temporaire vers PostgreSQL 15..."
    sed -i 's/postgres:16-alpine/postgres:15-alpine/g' docker-compose.yml
fi

# Démarrer seulement la base de données
log_info "Démarrage de PostgreSQL 15 pour sauvegarde..."
docker-compose up -d db

# Attendre que la base soit prête
log_info "Attente de la base de données..."
sleep 10

# Vérifier que la base est accessible
if ! docker-compose exec -T db pg_isready -U md_user -d md_agricole_db > /dev/null 2>&1; then
    log_error "La base de données n'est pas accessible"
    exit 1
fi

# Créer la sauvegarde
log_info "Création de la sauvegarde..."
docker-compose exec -T db pg_dump -U md_user -d md_agricole_db --clean --if-exists > $BACKUP_FILE

if [ ! -s "$BACKUP_FILE" ]; then
    log_error "La sauvegarde a échoué ou est vide"
    exit 1
fi

BACKUP_SIZE=$(du -h $BACKUP_FILE | cut -f1)
log_success "Sauvegarde créée : $BACKUP_FILE ($BACKUP_SIZE)"

# 2. Arrêter les services
log_info "2/6 - Arrêt des services..."
docker-compose down

# 3. Supprimer l'ancien volume PostgreSQL
log_info "3/6 - Suppression de l'ancien volume PostgreSQL 15..."
docker volume rm md_agricole_postgres_data 2>/dev/null || log_warning "Volume déjà supprimé ou inexistant"

# 4. Modifier docker-compose.yml pour PostgreSQL 16
log_info "4/6 - Configuration pour PostgreSQL 16..."
sed -i 's/postgres:15-alpine/postgres:16-alpine/g' docker-compose.yml
log_success "Configuration mise à jour vers PostgreSQL 16"

# 5. Démarrer avec PostgreSQL 16
log_info "5/6 - Démarrage de PostgreSQL 16..."
docker-compose up -d db

# Attendre que la nouvelle base soit prête
log_info "Attente de la nouvelle base PostgreSQL 16..."
sleep 15

# Vérifier que la nouvelle base est accessible
RETRY_COUNT=0
MAX_RETRIES=30

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if docker-compose exec -T db pg_isready -U md_user -d md_agricole_db > /dev/null 2>&1; then
        log_success "PostgreSQL 16 est prêt"
        break
    fi
    
    RETRY_COUNT=$((RETRY_COUNT + 1))
    log_info "Tentative $RETRY_COUNT/$MAX_RETRIES..."
    sleep 2
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    log_error "PostgreSQL 16 ne répond pas après $MAX_RETRIES tentatives"
    exit 1
fi

# 6. Restaurer les données
log_info "6/6 - Restauration des données dans PostgreSQL 16..."
docker-compose exec -T db psql -U md_user -d md_agricole_db < $BACKUP_FILE

if [ $? -eq 0 ]; then
    log_success "Données restaurées avec succès"
else
    log_error "Échec de la restauration des données"
    log_info "Fichier de sauvegarde disponible : $BACKUP_FILE"
    exit 1
fi

# Vérification finale
log_info "Vérification finale..."
TABLE_COUNT=$(docker-compose exec -T db psql -U md_user -d md_agricole_db -t -c "SELECT COUNT(*) FROM information_schema.tables WHERE table_schema = 'public';" | tr -d ' ')

if [ "$TABLE_COUNT" -gt 0 ]; then
    log_success "Migration réussie ! $TABLE_COUNT tables trouvées"
    
    # Statistiques rapides
    log_info "Statistiques de la base migrée :"
    docker-compose exec -T db psql -U md_user -d md_agricole_db -c "
        SELECT 
            'users' as table_name, COUNT(*) as count FROM users
        UNION ALL
        SELECT 'products', COUNT(*) FROM products
        UNION ALL
        SELECT 'orders', COUNT(*) FROM orders
        UNION ALL
        SELECT 'invoices', COUNT(*) FROM invoices
        UNION ALL
        SELECT 'categories', COUNT(*) FROM categories;
    " 2>/dev/null || log_warning "Impossible de récupérer les statistiques (tables Prisma pas encore créées ?)"
    
else
    log_error "Migration échouée (aucune table trouvée)"
    exit 1
fi

# Démarrer l'application complète
log_info "Démarrage de l'application complète..."
docker-compose up -d

# Attendre que l'application soit prête
log_info "Attente de l'application..."
sleep 10

# Test de santé
if curl -f -s http://localhost:3000/api/health > /dev/null 2>&1; then
    log_success "Application démarrée avec succès"
else
    log_warning "L'application ne répond pas encore, vérifiez les logs :"
    echo "docker-compose logs app"
fi

# Nettoyage
log_info "Nettoyage..."
mkdir -p backups
mv $BACKUP_FILE backups/
log_info "Sauvegarde déplacée vers : backups/$BACKUP_FILE"

echo ""
log_success "🎉 Migration PostgreSQL 15 → 16 terminée avec succès !"
echo ""
log_info "📋 Résumé :"
echo "   - PostgreSQL mis à jour de 15 à 16"
echo "   - Toutes les données préservées"
echo "   - Sauvegarde disponible : backups/$BACKUP_FILE"
echo ""
log_info "🌐 Votre application est accessible sur :"
echo "   - http://localhost:3000"
echo "   - Admin : http://localhost:3000/admin"
