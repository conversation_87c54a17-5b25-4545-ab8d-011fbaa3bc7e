#!/bin/bash

# Script de nettoyage complet pour redémarrer MD Agricole
# Usage: ./clean-restart.sh

set -e

# Couleurs
GREEN='\033[0;32m'
YELLOW='\033[1;33m'
RED='\033[0;31m'
BLUE='\033[0;34m'
NC='\033[0m'

log_info() {
    echo -e "${BLUE}ℹ️  $1${NC}"
}

log_success() {
    echo -e "${GREEN}✅ $1${NC}"
}

log_warning() {
    echo -e "${YELLOW}⚠️  $1${NC}"
}

log_error() {
    echo -e "${RED}❌ $1${NC}"
}

echo "🧹 Nettoyage complet MD Agricole"
echo "================================"

# Confirmation
echo ""
log_warning "⚠️  ATTENTION : Cette opération va :"
echo "1. Arrêter tous les conteneurs"
echo "2. Supprimer tous les volumes Docker (PERTE DE DONNÉES)"
echo "3. Supprimer les images Docker"
echo "4. Redémarrer avec une base de données vide"
echo ""
log_error "🚨 TOUTES VOS DONNÉES SERONT PERDUES !"
echo ""
read -p "Êtes-vous ABSOLUMENT sûr de vouloir continuer ? (SUPPRIMER/non): " -r
if [[ ! $REPLY == "SUPPRIMER" ]]; then
    echo "Nettoyage annulé."
    exit 0
fi

# 1. Arrêter tous les services
log_info "1/6 - Arrêt de tous les services..."
docker-compose down --remove-orphans || true

# 2. Supprimer les volumes
log_info "2/6 - Suppression des volumes..."
docker volume rm md_agricole_postgres_data 2>/dev/null || log_warning "Volume postgres_data déjà supprimé"
docker volume rm md_agricole_uploads 2>/dev/null || log_warning "Volume uploads déjà supprimé"

# Supprimer tous les volumes orphelins
docker volume prune -f

# 3. Supprimer les images
log_info "3/6 - Suppression des images..."
docker image rm md_agricole_app 2>/dev/null || log_warning "Image app déjà supprimée"
docker image prune -f

# 4. Nettoyer le système Docker
log_info "4/6 - Nettoyage du système Docker..."
docker system prune -f

# 5. Reconstruire et démarrer
log_info "5/6 - Reconstruction et démarrage..."
docker-compose build --no-cache
docker-compose up -d

# 6. Attendre que les services soient prêts
log_info "6/6 - Attente des services..."
sleep 20

# Vérifier PostgreSQL
log_info "Vérification de PostgreSQL..."
RETRY_COUNT=0
MAX_RETRIES=30

while [ $RETRY_COUNT -lt $MAX_RETRIES ]; do
    if docker-compose exec -T db pg_isready -U md_user -d md_agricole_db > /dev/null 2>&1; then
        log_success "PostgreSQL est prêt"
        break
    fi
    
    RETRY_COUNT=$((RETRY_COUNT + 1))
    log_info "Tentative $RETRY_COUNT/$MAX_RETRIES..."
    sleep 2
done

if [ $RETRY_COUNT -eq $MAX_RETRIES ]; then
    log_error "PostgreSQL ne répond pas"
    log_info "Vérifiez les logs : docker-compose logs db"
    exit 1
fi

# Initialiser Prisma
log_info "Initialisation de Prisma..."
docker-compose exec app npx prisma db push --force-reset || log_warning "Prisma db push a échoué"
docker-compose exec app npx prisma generate || log_warning "Prisma generate a échoué"

# Seeding optionnel
read -p "Voulez-vous créer des données de test ? (o/n): " -r
if [[ $REPLY =~ ^[Oo]$ ]]; then
    log_info "Création des données de test..."
    docker-compose exec app npm run db:seed 2>/dev/null || log_warning "Seeding échoué (script non trouvé ?)"
fi

# Test de santé final
log_info "Test de santé final..."
sleep 5

if curl -f -s http://localhost:3000/api/health > /dev/null 2>&1; then
    log_success "Application démarrée avec succès"
else
    log_warning "L'application ne répond pas encore"
    log_info "Vérifiez les logs : docker-compose logs app"
fi

echo ""
log_success "🎉 Nettoyage et redémarrage terminés !"
echo ""
log_info "🌐 Votre application est accessible sur :"
echo "   - http://localhost:3000"
echo "   - Admin : http://localhost:3000/admin"
echo ""
log_info "📋 Prochaines étapes :"
echo "   1. Créez un compte admin"
echo "   2. Configurez vos catégories et produits"
echo "   3. Testez les fonctionnalités"
echo ""
log_info "🔧 Commandes utiles :"
echo "   - Logs : docker-compose logs -f"
echo "   - Redémarrer : docker-compose restart"
echo "   - Arrêter : docker-compose down"
