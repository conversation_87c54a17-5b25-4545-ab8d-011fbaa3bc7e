
@import url('https://fonts.googleapis.com/css2?family=Inter:wght@100;200;300;400;500;600;700;800;900&display=swap');
@tailwind base;
@tailwind components;
@tailwind utilities;

/* Corrections pour éviter les problèmes de navigation */
* {
  -webkit-tap-highlight-color: transparent;
}

/* S'assurer que les liens et boutons sont toujours cliquables */
a, button, [role="button"] {
  pointer-events: auto !important;
  user-select: none;
}

/* Éviter les problèmes avec les overlays */
[data-radix-popper-content-wrapper] {
  z-index: 9999 !important;
}

/* Corrections pour les dialogs */
[data-state="open"] {
  pointer-events: auto !important;
}

/* Éviter les conflits d'événements */
.no-pointer-events {
  pointer-events: none;
}

.force-pointer-events {
  pointer-events: auto !important;
}

@layer base {
  :root {
    /* Couleurs principales pour MD Agricole */
    --background: 0 0% 100%;
    --foreground: 240 10% 3.9%;
    --card: 0 0% 100%;
    --card-foreground: 240 10% 3.9%;
    --popover: 0 0% 100%;
    --popover-foreground: 240 10% 3.9%;
    --primary: 142 76% 36%;
    --primary-foreground: 355 100% 97%;
    --secondary: 240 4.8% 95.9%;
    --secondary-foreground: 240 5.9% 10%;
    --muted: 240 4.8% 95.9%;
    --muted-foreground: 240 3.8% 46.1%;
    --accent: 142 76% 36%;
    --accent-foreground: 355 100% 97%;
    --destructive: 0 84.2% 60.2%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 5.9% 90%;
    --input: 240 5.9% 90%;
    --ring: 142 76% 36%;
    --radius: 0.75rem;

    /* Variables personnalisées */
    --agricultural-primary: 142 76% 36%;
    --agricultural-secondary: 47 96% 89%;
    --earth-tone: 39 100% 50%;
  }

  .dark {
    --background: 240 10% 3.9%;
    --foreground: 0 0% 98%;
    --card: 240 10% 3.9%;
    --card-foreground: 0 0% 98%;
    --popover: 240 10% 3.9%;
    --popover-foreground: 0 0% 98%;
    --primary: 142 76% 36%;
    --primary-foreground: 355 100% 97%;
    --secondary: 240 3.7% 15.9%;
    --secondary-foreground: 0 0% 98%;
    --muted: 240 3.7% 15.9%;
    --muted-foreground: 240 5% 64.9%;
    --accent: 142 76% 36%;
    --accent-foreground: 355 100% 97%;
    --destructive: 0 62.8% 30.6%;
    --destructive-foreground: 0 0% 98%;
    --border: 240 3.7% 15.9%;
    --input: 240 3.7% 15.9%;
    --ring: 142 76% 36%;
  }
}

@layer base {
  * {
    @apply border-border;
  }

  body {
    @apply bg-background text-foreground font-sans antialiased;
    font-family: 'Inter', system-ui, -apple-system, sans-serif;
    line-height: 1.6;
  }

  h1, h2, h3, h4, h5, h6 {
    @apply font-semibold tracking-tight;
  }

  h1 {
    @apply text-4xl lg:text-5xl xl:text-6xl;
  }

  h2 {
    @apply text-3xl lg:text-4xl;
  }

  h3 {
    @apply text-2xl lg:text-3xl;
  }
}

@layer components {
  .btn-primary {
    @apply bg-primary text-primary-foreground hover:bg-primary/90 px-6 py-3 rounded-lg font-medium transition-all duration-200 shadow-soft hover:shadow-medium;
  }

  .btn-secondary {
    @apply bg-secondary text-secondary-foreground hover:bg-secondary/80 px-6 py-3 rounded-lg font-medium transition-all duration-200 border border-border;
  }

  .card-modern {
    @apply bg-card text-card-foreground rounded-xl shadow-soft hover:shadow-medium transition-all duration-300 border border-border/50;
  }

  .glass-effect {
    @apply bg-white/10 backdrop-blur-md border border-white/20 shadow-soft;
  }

  .gradient-agricultural {
    @apply bg-gradient-to-br from-agricultural-400 to-agricultural-600;
  }

  .gradient-earth {
    @apply bg-gradient-to-br from-earth-400 to-earth-600;
  }

  .section-padding {
    @apply py-16 lg:py-24;
  }

  .container-custom {
    @apply max-w-7xl mx-auto px-4 sm:px-6 lg:px-8;
  }
}

@layer utilities {
  .animate-count-up {
    animation: count-up 2s ease-out forwards;
  }

  .parallax-bg {
    background-attachment: fixed;
    background-position: center;
    background-repeat: no-repeat;
    background-size: cover;
  }

  .text-gradient {
    @apply bg-gradient-to-r from-primary to-agricultural-500 bg-clip-text text-transparent;
  }

  .hover-lift {
    @apply transition-transform duration-300 hover:-translate-y-2;
  }

  .fade-in-up {
    @apply animate-fade-in;
  }

  .slide-in-left {
    @apply animate-slide-in-left;
  }

  .slide-in-right {
    @apply animate-slide-in-right;
  }
}
