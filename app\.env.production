# Configuration de production pour MD Agricole
# IMPORTANT: Changez toutes les valeurs par défaut avant le déploiement !

# Base de données PostgreSQL
DATABASE_URL="**********************************************************/md_agricole"
POSTGRES_PASSWORD="md_secure_password_2024"
POSTGRES_PORT=5432

# NextAuth.js - CHANGEZ CES VALEURS !
NEXTAUTH_URL="https://mdagricole.zidani.org"
NEXTAUTH_SECRET="md-agricole-super-secret-key-2024-changez-moi-en-production"

# Application
NODE_ENV="production"
APP_PORT=3007

# Upload de fichiers
UPLOAD_DIR="/app/public/uploads"
MAX_FILE_SIZE="10485760"  # 10MB

# Email (optionnel - pour les notifications)
SMTP_HOST="smtp.gmail.com"
SMTP_PORT="587"
SMTP_USER="<EMAIL>"
SMTP_PASSWORD="votre-mot-de-passe-app"
SMTP_FROM="<EMAIL>"

# Redis (optionnel - pour le cache)
REDIS_URL="redis://:redis_secure_password@redis:6379"
REDIS_PASSWORD="redis_secure_password"

# Sécurité
ALLOWED_ORIGINS="https://mdagricole.zidani.org,https://www.mdagricole.zidani.org"

# Logs
LOG_LEVEL="info"

# Backup automatique (optionnel)
BACKUP_ENABLED="true"
BACKUP_SCHEDULE="0 2 * * *"  # Tous les jours à 2h du matin
BACKUP_RETENTION_DAYS="30"

# Monitoring (optionnel)
ENABLE_METRICS="true"
METRICS_PORT="9090"
